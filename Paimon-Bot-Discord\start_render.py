#!/usr/bin/env python3
"""
🚀 Script de Inicio para Render - Paimon Bot
Maneja errores de dependencias y inicia el bot de forma robusta
"""

import sys
import os
import time
import subprocess

def check_and_install_missing():
    """Verifica e instala dependencias faltantes"""
    print("🔍 Verificando dependencias críticas...")
    
    critical_deps = {
        'discord': 'discord.py>=2.3.2',
        'flask': 'flask>=2.0.0', 
        'aiohttp': 'aiohttp>=3.8.0',
        'requests': 'requests>=2.28.0',
        'psutil': 'psutil>=5.9.0'
    }
    
    missing = []
    
    for module, package in critical_deps.items():
        try:
            __import__(module)
            print(f"✅ {module} - OK")
        except ImportError:
            print(f"❌ {module} - FALTA")
            missing.append(package)
    
    if missing:
        print(f"📦 Instalando {len(missing)} dependencias faltantes...")
        for package in missing:
            try:
                subprocess.run([sys.executable, '-m', 'pip', 'install', package], 
                             check=True, capture_output=True)
                print(f"✅ Instalado: {package}")
            except subprocess.CalledProcessError as e:
                print(f"❌ Error instalando {package}: {e}")
    
    return len(missing) == 0

def setup_environment():
    """Configura el entorno para Render"""
    print("🔧 Configurando entorno para Render...")
    
    # Configurar variables de entorno por defecto
    if not os.getenv('PORT'):
        os.environ['PORT'] = '8080'
        print("📡 Puerto configurado: 8080")
    
    # Verificar token de Discord
    if not os.getenv('TOKEN'):
        print("⚠️ TOKEN de Discord no configurado")
        print("   Configúralo en Render Dashboard > Environment")
        return False
    else:
        print("✅ TOKEN de Discord configurado")
    
    return True

def start_bot():
    """Inicia el bot principal"""
    print("🤖 Iniciando Paimon Bot...")
    
    try:
        # Importar y ejecutar el bot
        import main
        print("✅ Bot iniciado exitosamente")
        
    except ImportError as e:
        print(f"❌ Error importando main.py: {e}")
        
        # Intentar con Bot.py como fallback
        try:
            import Bot
            print("✅ Bot iniciado desde Bot.py")
        except ImportError as e2:
            print(f"❌ Error importando Bot.py: {e2}")
            return False
            
    except Exception as e:
        print(f"❌ Error iniciando el bot: {e}")
        return False
    
    return True

def main():
    """Función principal"""
    print("🚀 Paimon Bot - Iniciador para Render")
    print("=" * 40)
    print(f"🐍 Python: {sys.version}")
    print(f"📍 Directorio: {os.getcwd()}")
    print(f"🌐 Puerto: {os.getenv('PORT', 'No configurado')}")
    print("=" * 40)
    
    # Verificar dependencias
    if not check_and_install_missing():
        print("⚠️ Algunas dependencias faltan, pero continuando...")
    
    # Configurar entorno
    if not setup_environment():
        print("❌ Error en configuración del entorno")
        sys.exit(1)
    
    # Iniciar bot
    max_retries = 3
    for attempt in range(max_retries):
        print(f"\n🔄 Intento {attempt + 1}/{max_retries}")
        
        if start_bot():
            print("✅ Bot iniciado exitosamente")
            break
        else:
            if attempt < max_retries - 1:
                print(f"⏳ Reintentando en 5 segundos...")
                time.sleep(5)
            else:
                print("❌ No se pudo iniciar el bot después de varios intentos")
                sys.exit(1)

if __name__ == "__main__":
    main()
