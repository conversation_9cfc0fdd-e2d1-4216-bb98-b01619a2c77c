#!/usr/bin/env python3
"""
🎮 Instalador de gd.py con versiones específicas
Instala wrapt==1.14.1 y gd.py==1.7.1 que son compatibles
"""

import subprocess
import sys

def run_command(command):
    """Ejecuta un comando y muestra el resultado"""
    print(f"🔄 Ejecutando: {command}")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ Completado exitosamente")
        if result.stdout.strip():
            print(f"   {result.stdout.strip()}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error: {e}")
        if e.stderr:
            print(f"   {e.stderr.strip()}")
        return False

def main():
    print("🎮 Instalador de gd.py - Versiones Específicas")
    print("=" * 50)
    
    # Paso 1: Desinstalar versiones problemáticas
    print("\n🧹 Limpiando instalaciones anteriores...")
    run_command("pip uninstall gd.py wrapt wraps -y")
    
    # Paso 2: Instalar wrapt específico primero
    print("\n📦 Instalando wrapt==1.14.1...")
    if not run_command("pip install wrapt==1.14.1"):
        print("❌ Error instalando wrapt")
        return
    
    # Paso 3: Instalar gd.py específico
    print("\n🎮 Instalando gd.py==1.0.1...")
    if not run_command("pip install gd.py==1.0.1"):
        print("❌ Error instalando gd.py")
        return
    
    # Paso 4: Instalar psutil
    print("\n⚙️ Instalando psutil...")
    run_command("pip install psutil>=5.9.0")
    
    # Paso 5: Probar importación
    print("\n🧪 Probando importación...")
    try:
        import gd
        print(f"✅ gd.py importado exitosamente - Versión: {gd.__version__}")
        
        # Probar funcionalidad básica
        client = gd.Client()
        print("✅ Cliente de gd.py creado exitosamente")
        
        print("\n🎉 ¡Instalación completada exitosamente!")
        print("💡 Ahora puedes ejecutar tu bot sin errores de gd.py")
        
    except Exception as e:
        print(f"❌ Error probando gd.py: {e}")
        print("⚠️ Puede que necesites reiniciar tu entorno")

if __name__ == "__main__":
    main()
