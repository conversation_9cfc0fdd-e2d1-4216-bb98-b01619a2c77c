#!/usr/bin/env python3
"""
🚀 Script de Instalación para Render - Paimon Bot
Instala dependencias y configura el bot para funcionar en Render
"""

import subprocess
import sys
import os
import time

def run_command(command, description=""):
    """Ejecuta un comando y maneja errores"""
    print(f"🔄 {description}")
    print(f"   Comando: {command}")
    
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            check=True, 
            capture_output=True, 
            text=True
        )
        print(f"✅ {description} - Completado")
        if result.stdout:
            print(f"   Output: {result.stdout.strip()}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} - Error")
        print(f"   Error: {e.stderr.strip() if e.stderr else str(e)}")
        return False

def install_dependencies():
    """Instala las dependencias paso a paso"""
    print("📦 Instalando dependencias para Render...")
    
    # Actualizar pip primero
    run_command("python -m pip install --upgrade pip", "Actualizando pip")
    
    # Instalar dependencias críticas primero
    critical_deps = [
        "discord.py>=2.3.2",
        "aiohttp>=3.8.0", 
        "flask>=2.0.0",
        "requests>=2.28.0",
        "python-dotenv>=1.0.0",
        "psutil>=5.9.0"
    ]
    
    print("\n🔧 Instalando dependencias críticas...")
    for dep in critical_deps:
        run_command(f"pip install '{dep}'", f"Instalando {dep}")
    
    # Instalar dependencias de seguridad
    security_deps = [
        "pycryptodome>=3.15.0",
        "cryptography>=3.4.8"
    ]
    
    print("\n🔐 Instalando dependencias de seguridad...")
    for dep in security_deps:
        run_command(f"pip install '{dep}'", f"Instalando {dep}")
    
    # Instalar dependencias de audio/media (opcionales)
    media_deps = [
        "beautifulsoup4>=4.11.0",
        "PyNaCl>=1.5.0",
        "pyttsx3",
        "gTTS",
        "pydub"
    ]
    
    print("\n🎵 Instalando dependencias de media...")
    for dep in media_deps:
        success = run_command(f"pip install '{dep}'", f"Instalando {dep}")
        if not success:
            print(f"⚠️ {dep} falló - continuando...")
    
    # Instalar dependencias de AI
    ai_deps = [
        "openai",
        "google-generativeai"
    ]
    
    print("\n🤖 Instalando dependencias de AI...")
    for dep in ai_deps:
        success = run_command(f"pip install '{dep}'", f"Instalando {dep}")
        if not success:
            print(f"⚠️ {dep} falló - continuando...")
    
    # Intentar instalar gd.py con sus dependencias
    print("\n🎮 Instalando dependencias de Geometry Dash...")
    gd_success = False
    
    # Método 1: Instalar wraps primero
    if run_command("pip install 'wraps>=0.13.0'", "Instalando wraps"):
        if run_command("pip install 'gd.py>=1.7.0'", "Instalando gd.py"):
            gd_success = True
    
    # Método 2: Si falla, intentar versiones específicas
    if not gd_success:
        print("🔄 Intentando versiones específicas...")
        if run_command("pip install 'wraps==0.13.0'", "Instalando wraps 0.13.0"):
            run_command("pip install 'gd.py==1.7.0'", "Instalando gd.py 1.7.0")
    
    print("\n✅ Instalación de dependencias completada")

def create_render_files():
    """Crea archivos necesarios para Render"""
    print("\n📄 Creando archivos de configuración para Render...")
    
    # Crear Procfile para Render
    procfile_content = "web: python main.py"
    
    try:
        with open("Procfile", "w") as f:
            f.write(procfile_content)
        print("✅ Procfile creado")
    except Exception as e:
        print(f"❌ Error creando Procfile: {e}")
    
    # Crear runtime.txt
    python_version = f"python-{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
    
    try:
        with open("runtime.txt", "w") as f:
            f.write(python_version)
        print(f"✅ runtime.txt creado ({python_version})")
    except Exception as e:
        print(f"❌ Error creando runtime.txt: {e}")

def verify_installation():
    """Verifica que las dependencias críticas estén instaladas"""
    print("\n🔍 Verificando instalación...")
    
    critical_modules = [
        ("discord", "Discord.py"),
        ("flask", "Flask"),
        ("aiohttp", "aiohttp"),
        ("requests", "Requests"),
        ("psutil", "psutil"),
        ("cryptography", "Cryptography")
    ]
    
    all_good = True
    
    for module, name in critical_modules:
        try:
            __import__(module)
            print(f"✅ {name} - OK")
        except ImportError:
            print(f"❌ {name} - FALTA")
            all_good = False
    
    # Verificar módulos opcionales
    optional_modules = [
        ("gd", "gd.py (Geometry Dash)"),
        ("openai", "OpenAI"),
        ("google.generativeai", "Google Generative AI")
    ]
    
    print("\n📋 Módulos opcionales:")
    for module, name in optional_modules:
        try:
            __import__(module)
            print(f"✅ {name} - OK")
        except ImportError:
            print(f"⚠️ {name} - No disponible (opcional)")
    
    return all_good

def main():
    """Función principal"""
    print("🚀 Configurador de Paimon Bot para Render")
    print("=" * 50)
    
    # Verificar que estamos en el directorio correcto
    if not os.path.exists("main.py"):
        print("❌ Error: main.py no encontrado")
        print("   Ejecuta este script desde el directorio del bot")
        return
    
    print("📍 Directorio correcto detectado")
    
    # Instalar dependencias
    install_dependencies()
    
    # Crear archivos de Render
    create_render_files()
    
    # Verificar instalación
    if verify_installation():
        print("\n🎉 ¡Instalación completada exitosamente!")
        print("\n📋 Próximos pasos para Render:")
        print("1. Sube tu código a GitHub")
        print("2. Conecta tu repositorio en Render")
        print("3. Configura las variables de entorno:")
        print("   - TOKEN (tu token de Discord)")
        print("   - OPENAI_API_KEY (opcional)")
        print("   - GEMINI_API_KEY (opcional)")
        print("   - GITHUB_TOKEN (opcional)")
        print("4. Deploy automático se iniciará")
        
        print("\n🔧 Comandos de build para Render:")
        print("   Build Command: python install_render.py")
        print("   Start Command: python main.py")
        
    else:
        print("\n⚠️ Instalación completada con errores")
        print("El bot debería funcionar, pero algunas funciones pueden no estar disponibles")

if __name__ == "__main__":
    main()
