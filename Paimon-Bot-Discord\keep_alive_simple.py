"""
Keep Alive Simple - Versión simplificada sin psutil
Mantiene el bot activo en Render y otras plataformas
"""

from flask import Flask, jsonify
from threading import Thread
import os
import time

app = Flask('')

# Variables globales para estadísticas
start_time = time.time()
request_count = 0

@app.route('/')
def home():
    """Página principal del servidor"""
    global request_count
    request_count += 1
    uptime = time.time() - start_time
    hours = int(uptime // 3600)
    minutes = int((uptime % 3600) // 60)

    return f"""
    <html>
    <head>
        <title>Paimon Bot - Status</title>
        <meta http-equiv="refresh" content="30">
        <style>
            body {{ 
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: #ffffff; 
                text-align: center; 
                padding: 50px;
                margin: 0;
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
            }}
            .container {{ 
                max-width: 600px; 
                background: rgba(255, 255, 255, 0.1);
                backdrop-filter: blur(10px);
                padding: 40px; 
                border-radius: 20px; 
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
                border: 1px solid rgba(255, 255, 255, 0.2);
            }}
            .status {{ 
                color: #4ade80; 
                font-size: 28px; 
                margin: 20px 0;
                font-weight: bold;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            }}
            .info {{ 
                background: rgba(255, 255, 255, 0.1);
                padding: 20px; 
                border-radius: 15px; 
                margin: 15px 0;
                border: 1px solid rgba(255, 255, 255, 0.1);
            }}
            .title {{
                font-size: 36px;
                margin-bottom: 10px;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            }}
            .subtitle {{
                font-size: 16px;
                opacity: 0.8;
                margin-bottom: 30px;
            }}
        </style>
    </head>
    <body>
        <div class="container">
            <h1 class="title">🤖 Paimon Bot</h1>
            <p class="subtitle">Discord Bot Helper - Render Deployment</p>
            <div class="status">✅ ONLINE & RUNNING</div>
            <div class="info">
                <strong>⏰ Uptime:</strong> {hours}h {minutes}m<br>
                <strong>📊 Requests:</strong> {request_count}<br>
                <strong>🔄 Auto-refresh:</strong> Every 30 seconds<br>
                <strong>🌐 Platform:</strong> Render
            </div>
            <p>This server keeps the bot alive 24/7 on Render</p>
        </div>
    </body>
    </html>
    """

@app.route('/status')
def status():
    """Endpoint de estado en JSON"""
    global request_count
    request_count += 1
    uptime = time.time() - start_time

    return jsonify({
        'status': 'online',
        'uptime_seconds': int(uptime),
        'uptime_formatted': f"{int(uptime // 3600)}h {int((uptime % 3600) // 60)}m",
        'requests': request_count,
        'platform': 'render',
        'bot_version': '2.1.95',
        'timestamp': int(time.time())
    })

@app.route('/ping')
def ping():
    """Endpoint simple para ping"""
    global request_count
    request_count += 1
    return "pong"

@app.route('/health')
def health():
    """Health check endpoint para Render"""
    return jsonify({
        'status': 'healthy',
        'timestamp': int(time.time()),
        'service': 'paimon-bot',
        'platform': 'render'
    })

@app.route('/metrics')
def metrics():
    """Métricas básicas del servidor"""
    global request_count
    uptime = time.time() - start_time
    
    return jsonify({
        'uptime_seconds': int(uptime),
        'total_requests': request_count,
        'requests_per_minute': round(request_count / (uptime / 60), 2) if uptime > 0 else 0,
        'start_time': int(start_time),
        'current_time': int(time.time())
    })

def run():
    """Ejecuta el servidor Flask"""
    port = int(os.environ.get('PORT', 8080))
    host = '0.0.0.0'
    
    print(f"🌐 Starting simple keep-alive server...")
    print(f"📡 Server will be available at: http://{host}:{port}")
    
    app.run(
        host=host, 
        port=port, 
        debug=False, 
        use_reloader=False,
        threaded=True
    )

def keep_alive():
    """Inicia el servidor en un hilo separado"""
    try:
        server = Thread(target=run)
        server.daemon = True
        server.start()
        print("✅ Simple keep-alive server started successfully")
        return True
    except Exception as e:
        print(f"❌ Error starting simple keep-alive server: {e}")
        return False

# Función de compatibilidad
def start_server():
    """Alias para keep_alive()"""
    return keep_alive()

if __name__ == "__main__":
    print("🚀 Starting Paimon Bot Keep-Alive Server (Simple)")
    run()
