#!/usr/bin/env python3
"""
🧪 Probador de versiones de gd.py
Encuentra la mejor combinación de wrapt + gd.py que funcione
"""

import subprocess
import sys

def run_command(command, silent=False):
    """Ejecuta un comando"""
    if not silent:
        print(f"🔄 {command}")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        return True, result.stdout
    except subprocess.CalledProcessError as e:
        return False, e.stderr

def test_gd_import():
    """Prueba si gd.py se puede importar"""
    try:
        # Lim<PERSON>r módulos cargados
        modules_to_remove = []
        for module_name in list(sys.modules.keys()):
            if module_name.startswith('gd') or module_name.startswith('wrap'):
                modules_to_remove.append(module_name)
        
        for module_name in modules_to_remove:
            if module_name in sys.modules:
                del sys.modules[module_name]
        
        import gd
        version = getattr(gd, '__version__', 'unknown')
        return True, version
    except Exception as e:
        return False, str(e)

def main():
    print("🧪 Probador de Versiones gd.py + wrapt")
    print("=" * 50)
    
    # Combinaciones a probar (wrapt_version, gd_version)
    combinations = [
        ("1.14.1", "1.0.1"),
        ("1.14.1", "1.0.0"), 
        ("1.14.1", "0.11.0"),
        ("1.14.1", "0.10.5"),
        ("1.13.1", "1.0.1"),
        ("1.13.1", "1.0.0"),
        ("1.12.1", "1.0.1"),
        ("1.12.1", "1.0.0")
    ]
    
    successful_combinations = []
    
    for wrapt_ver, gd_ver in combinations:
        print(f"\n🔄 Probando wrapt=={wrapt_ver} + gd.py=={gd_ver}")
        
        # Limpiar instalaciones anteriores
        run_command("pip uninstall gd.py wrapt wraps -y", silent=True)
        
        # Instalar wrapt
        success, _ = run_command(f"pip install wrapt=={wrapt_ver}", silent=True)
        if not success:
            print(f"❌ Error instalando wrapt {wrapt_ver}")
            continue
        
        # Instalar gd.py
        success, _ = run_command(f"pip install gd.py=={gd_ver}", silent=True)
        if not success:
            print(f"❌ Error instalando gd.py {gd_ver}")
            continue
        
        # Probar importación
        import_success, result = test_gd_import()
        if import_success:
            print(f"✅ FUNCIONA: wrapt {wrapt_ver} + gd.py {gd_ver} (versión: {result})")
            successful_combinations.append((wrapt_ver, gd_ver, result))
        else:
            print(f"❌ Error: {result}")
    
    print("\n" + "=" * 50)
    print("📋 RESULTADOS:")
    
    if successful_combinations:
        print("✅ Combinaciones que funcionan:")
        for wrapt_ver, gd_ver, gd_actual_ver in successful_combinations:
            print(f"   • wrapt=={wrapt_ver} + gd.py=={gd_ver} (v{gd_actual_ver})")
        
        # Usar la primera combinación exitosa
        best_wrapt, best_gd, _ = successful_combinations[0]
        print(f"\n🎯 RECOMENDACIÓN: wrapt=={best_wrapt} + gd.py=={best_gd}")
        
        # Instalar la mejor combinación
        print(f"\n🚀 Instalando combinación recomendada...")
        run_command("pip uninstall gd.py wrapt wraps -y", silent=True)
        run_command(f"pip install wrapt=={best_wrapt}")
        run_command(f"pip install gd.py=={best_gd}")
        
        # Verificación final
        final_success, final_version = test_gd_import()
        if final_success:
            print(f"🎉 ¡INSTALACIÓN EXITOSA! gd.py v{final_version}")
        else:
            print(f"❌ Error en verificación final: {final_version}")
            
    else:
        print("❌ Ninguna combinación funcionó")
        print("💡 Soluciones alternativas:")
        print("   1. Usar el bot sin gd.py (funcionalidad limitada)")
        print("   2. Instalar desde GitHub: pip install git+https://github.com/NeKitDS/gd.py.git")
        print("   3. Usar el fallback con GDBrowser API")

if __name__ == "__main__":
    main()
