#!/usr/bin/env python3
"""
🔧 Solucionador definitivo del problema wraps/gd.py
Implementa múltiples estrategias para hacer funcionar gd.py
"""

import subprocess
import sys
import os
import importlib.util

def run_command(command, description="", silent=False):
    """Ejecuta un comando y maneja errores"""
    if not silent:
        print(f"🔄 {description}")
        print(f"   Comando: {command}")
    
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            check=True, 
            capture_output=True, 
            text=True
        )
        if not silent:
            print(f"✅ {description} - Completado")
        return True, result.stdout
    except subprocess.CalledProcessError as e:
        if not silent:
            print(f"❌ {description} - Error")
            if e.stderr:
                print(f"   Error: {e.stderr.strip()}")
        return False, e.stderr

def clean_previous_installations():
    """Limpia instalaciones problemáticas"""
    print("🧹 Limpiando instalaciones anteriores...")
    
    packages_to_remove = [
        'gd.py', 'wrapt', 'wraps', 'aiohttp-requests'
    ]
    
    for package in packages_to_remove:
        run_command(f"pip uninstall {package} -y", f"Desinstalando {package}", silent=True)

def install_method_1():
    """Método 1: Instalación con versiones específicas"""
    print("\n🔧 Método 1: Instalación con versiones específicas")
    
    steps = [
        ("pip install wrapt==1.14.1", "Instalando wrapt 1.14.1"),
        ("pip install aiohttp==3.8.6", "Instalando aiohttp específico"),
        ("pip install gd.py==1.0.1", "Instalando gd.py 1.0.1")
    ]
    
    for command, description in steps:
        success, _ = run_command(command, description)
        if not success:
            return False
    
    return test_gd_import()

def install_method_2():
    """Método 2: Instalación desde GitHub"""
    print("\n🔧 Método 2: Instalación desde GitHub")
    
    github_versions = [
        "git+https://github.com/NeKitDS/gd.py.git@v1.0.1",
        "git+https://github.com/NeKitDS/gd.py.git@v1.0.0",
        "git+https://github.com/NeKitDS/gd.py.git"
    ]
    
    # Instalar wrapt primero
    success, _ = run_command("pip install wrapt==1.14.1", "Instalando wrapt")
    if not success:
        return False
    
    for github_url in github_versions:
        print(f"📥 Probando: {github_url}")
        success, _ = run_command(f"pip install '{github_url}'", f"Instalando desde GitHub")
        if success and test_gd_import():
            return True
        # Si falla, desinstalar y probar siguiente
        run_command("pip uninstall gd.py -y", silent=True)
    
    return False

def install_method_3():
    """Método 3: Crear módulos dummy y usar versión básica"""
    print("\n🔧 Método 3: Módulos dummy + instalación básica")
    
    # Crear directorio wraps dummy
    try:
        os.makedirs("wraps", exist_ok=True)
        
        # Crear __init__.py
        with open("wraps/__init__.py", "w") as f:
            f.write('"""Módulo dummy para wraps"""\n')
        
        # Crear early.py
        with open("wraps/early.py", "w") as f:
            f.write('"""Módulo dummy para wraps.early"""\n')
        
        # Crear primitives.py  
        with open("wraps/primitives.py", "w") as f:
            f.write('"""Módulo dummy para wraps.primitives"""\n')
        
        print("✅ Módulos dummy creados")
        
        # Instalar gd.py
        success, _ = run_command("pip install gd.py==1.0.1", "Instalando gd.py con módulos dummy")
        if success:
            return test_gd_import()
            
    except Exception as e:
        print(f"❌ Error creando módulos dummy: {e}")
    
    return False

def test_gd_import():
    """Prueba si gd.py se puede importar correctamente"""
    try:
        # Limpiar módulos cargados
        modules_to_remove = []
        for module_name in list(sys.modules.keys()):
            if any(name in module_name.lower() for name in ['gd', 'wrap']):
                modules_to_remove.append(module_name)
        
        for module_name in modules_to_remove:
            if module_name in sys.modules:
                del sys.modules[module_name]
        
        # Intentar importar
        import gd
        version = getattr(gd, '__version__', 'unknown')
        
        # Probar funcionalidad básica
        client = gd.Client()
        
        print(f"✅ gd.py funciona correctamente - Versión: {version}")
        return True
        
    except Exception as e:
        print(f"❌ Error probando gd.py: {e}")
        return False

def create_fallback_import():
    """Crea un archivo de importación fallback"""
    print("\n🛠️ Creando sistema de fallback...")
    
    fallback_code = '''"""
Sistema de fallback para gd.py
Se usa cuando gd.py no está disponible
"""

import aiohttp
import asyncio
import json
from typing import Optional, Dict, Any, List

class GDLevel:
    """Representa un nivel de Geometry Dash"""
    def __init__(self, data: dict):
        self.id = data.get('id', 0)
        self.name = data.get('name', 'Unknown')
        self.creator = data.get('creator', 'Unknown') 
        self.difficulty = data.get('difficulty', 'Unknown')
        self.stars = data.get('stars', 0)
        self.downloads = data.get('downloads', 0)
        self.likes = data.get('likes', 0)
        self.description = data.get('description', '')
        self.length = data.get('length', 'Unknown')
        self.song = data.get('song', 'Unknown')

class GDUser:
    """Representa un usuario de Geometry Dash"""
    def __init__(self, data: dict):
        self.name = data.get('name', 'Unknown')
        self.id = data.get('id', 0)
        self.stars = data.get('stars', 0)
        self.demons = data.get('demons', 0)
        self.cp = data.get('cp', 0)

class GDClient:
    """Cliente fallback para Geometry Dash usando APIs públicas"""
    
    def __init__(self):
        self.base_url = "https://gdbrowser.com/api"
        self.session = None
    
    async def get_session(self):
        """Obtiene o crea una sesión aiohttp"""
        if self.session is None or self.session.closed:
            self.session = aiohttp.ClientSession()
        return self.session
    
    async def close(self):
        """Cierra la sesión"""
        if self.session and not self.session.closed:
            await self.session.close()
    
    async def get_level(self, level_id: int) -> Optional[GDLevel]:
        """Obtiene información de un nivel"""
        try:
            session = await self.get_session()
            async with session.get(f"{self.base_url}/level/{level_id}") as response:
                if response.status == 200:
                    data = await response.json()
                    return GDLevel(data)
            return None
        except Exception as e:
            print(f"Error obteniendo nivel {level_id}: {e}")
            return None
    
    async def search_levels(self, query: str, limit: int = 10) -> List[GDLevel]:
        """Busca niveles por nombre"""
        try:
            session = await self.get_session()
            params = {'str': query, 'count': limit}
            async with session.get(f"{self.base_url}/search", params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    return [GDLevel(level) for level in data.get('results', [])]
            return []
        except Exception as e:
            print(f"Error buscando niveles: {e}")
            return []
    
    async def get_user(self, username: str) -> Optional[GDUser]:
        """Obtiene información de un usuario"""
        try:
            session = await self.get_session()
            async with session.get(f"{self.base_url}/profile/{username}") as response:
                if response.status == 200:
                    data = await response.json()
                    return GDUser(data)
            return None
        except Exception as e:
            print(f"Error obteniendo usuario {username}: {e}")
            return None

# Crear instancia global para compatibilidad
Client = GDClient

# Variables de compatibilidad
__version__ = "fallback-1.0.0"

print("🔄 Usando sistema de fallback para gd.py")
'''
    
    try:
        with open('gd_fallback.py', 'w', encoding='utf-8') as f:
            f.write(fallback_code)
        print("✅ Sistema de fallback creado: gd_fallback.py")
        return True
    except Exception as e:
        print(f"❌ Error creando fallback: {e}")
        return False

def main():
    """Función principal"""
    print("🔧 Solucionador Definitivo de gd.py + wraps")
    print("=" * 60)
    
    # Limpiar instalaciones anteriores
    clean_previous_installations()
    
    # Probar diferentes métodos
    methods = [
        install_method_1,
        install_method_2, 
        install_method_3
    ]
    
    for i, method in enumerate(methods, 1):
        print(f"\n{'='*20} MÉTODO {i} {'='*20}")
        
        if method():
            print(f"\n🎉 ¡ÉXITO! Método {i} funcionó correctamente")
            print("✅ gd.py está listo para usar")
            return
        else:
            print(f"❌ Método {i} falló, probando siguiente...")
            clean_previous_installations()
    
    # Si todos los métodos fallan, crear fallback
    print(f"\n{'='*20} FALLBACK {'='*20}")
    print("❌ Todos los métodos fallaron")
    print("🛠️ Creando sistema de fallback...")
    
    if create_fallback_import():
        print("\n💡 SOLUCIÓN ALTERNATIVA:")
        print("- El bot funcionará con funcionalidad limitada de GD")
        print("- Usa APIs públicas en lugar de gd.py")
        print("- Todas las funciones básicas seguirán funcionando")
        print("\n📝 Para usar el fallback en tu código:")
        print("   try:")
        print("       import gd")
        print("   except ImportError:")
        print("       import gd_fallback as gd")
    else:
        print("❌ No se pudo crear el sistema de fallback")

if __name__ == "__main__":
    main()
