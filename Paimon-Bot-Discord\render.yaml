# Configuración para Render - Paimon Bot
services:
  - type: web
    name: paimon-bot
    env: python
    buildCommand: |
      pip install --upgrade pip
      pip install -r requirements.txt
      python install_render.py
    startCommand: python main.py
    envVars:
      - key: PYTHON_VERSION
        value: 3.11.0
      - key: PORT
        generateValue: true
      - key: TOKEN
        sync: false  # Configurar manualmente en Render Dashboard
      - key: OPENAI_API_KEY
        sync: false  # Opcional
      - key: GEMINI_API_KEY  
        sync: false  # Opcional
      - key: GITHUB_TOKEN
        sync: false  # Opcional
    healthCheckPath: /health
    autoDeploy: true
    
# Configuración adicional
region: oregon  # Cambia según tu preferencia
plan: free      # O el plan que prefieras
